async function generatePDF() {
  const { jsPDF } = window.jspdf;
  const doc = new jsPDF();

  // Logo placeholder
  doc.addImage('logo.png', 'PNG', 10, 10, 40, 20);

  doc.setFontSize(16);
  doc.text("SahlaMove - Feuille de mission / Reçu client", 60, 20);

  doc.setFontSize(12);
  let y = 40;

  // Client info
  doc.text(`Nom du client: ${document.getElementById('clientName').value}`, 10, y);
  y += 7;
  doc.text(`Téléphone: ${document.getElementById('clientPhone').value}`, 10, y);
  y += 10;

  // Adresse de départ
  doc.text("Adresse de départ:", 10, y);
  y += 7;
  doc.text(`Rue: ${document.getElementById('fromStreet').value}`, 10, y);
  y += 7;
  doc.text(`Appartement: ${document.getElementById('fromApt').value}`, 10, y);
  y += 7;
  doc.text(`Étage: ${document.getElementById('fromFloor').value} | Ascenseur: ${document.getElementById('fromElevator').value}`, 10, y);
  y += 10;

  // Adresse d’arrivée
  doc.text("Adresse d'arrivée:", 10, y);
  y += 7;
  doc.text(`Rue: ${document.getElementById('toStreet').value}`, 10, y);
  y += 7;
  doc.text(`Appartement: ${document.getElementById('toApt').value}`, 10, y);
  y += 7;
  doc.text(`Étage: ${document.getElementById('toFloor').value} | Ascenseur: ${document.getElementById('toElevator').value}`, 10, y);
  y += 10;

  // Options
  const packing = document.getElementById('packing').checked ? "Oui" : "Non";
  const assembly = document.getElementById('assembly').checked ? "Oui" : "Non";
  const fragile = document.getElementById('fragile').checked ? "Oui" : "Non";

  doc.text(`Emballage/Déballage: ${packing}`, 10, y);
  y += 7;
  doc.text(`Montage/Démontage: ${assembly}`, 10, y);
  y += 7;
  doc.text(`Protection spéciale: ${fragile}`, 10, y);
  y += 10;

  // Inventory
  doc.text("Inventaire des biens:", 10, y);
  y += 7;

  const items = document.querySelectorAll('.inventoryItem');
  items.forEach((item, index) => {
    const desc = item.querySelector('.itemDesc').value;
    const qty = item.querySelector('.itemQty').value;
    const assemblyVal = item.querySelector('.itemAssembly').value;
    doc.text(`${index + 1}. ${desc} | Quantité: ${qty} | Assemblage: ${assemblyVal}`, 10, y);
    y += 7;
    if (y > 280) { doc.addPage(); y = 20; } // new page if overflow
  });

  // Footer / Signature placeholders
  y += 10;
  doc.text("Signatures:", 10, y);
  y += 7;
  doc.text("Client: ____________________   Office Manager: ____________________   Ouvrier: ____________________", 10, y);

  doc.save("SahlaMove_Recu.pdf");
}

// Add new inventory item
document.getElementById('addItem').addEventListener('click', () => {
  const container = document.getElementById('inventoryContainer');
  const div = document.createElement('div');
  div.className = 'inventoryItem';
  div.innerHTML = `
    <input type="text" placeholder="Description" class="itemDesc">
    <input type="number" placeholder="Quantité" class="itemQty">
    <select class="itemAssembly">
      <option value="Non">Assemblage: Non</option>
      <option value="Oui">Assemblage: Oui</option>
    </select>
  `;
  container.appendChild(div);
});
