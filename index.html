<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>SahlaMove - Générateur de reçu</title>
  <link rel="stylesheet" href="style.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
  <div class="container">
    <h1>SahlaMove - Formulaire de mission</h1>
    <form id="missionForm">
      <h2>Informations client</h2>
      <label>Nom du client: <input type="text" id="clientName" required></label>
      <label>Téléphone: <input type="text" id="clientPhone" required></label>

      <h2>Adresse de départ</h2>
      <label>Rue: <input type="text" id="fromStreet" required></label>
      <label>Appartement: <input type="text" id="fromApt"></label>
      <label>Étage: <input type="number" id="fromFloor"></label>
      <label>Ascenseur: 
        <select id="fromElevator">
          <option value="Oui">Oui</option>
          <option value="Non">Non</option>
        </select>
      </label>

      <h2>Adresse d’arrivée</h2>
      <label>Rue: <input type="text" id="toStreet" required></label>
      <label>Appartement: <input type="text" id="toApt"></label>
      <label>Étage: <input type="number" id="toFloor"></label>
      <label>Ascenseur: 
        <select id="toElevator">
          <option value="Oui">Oui</option>
          <option value="Non">Non</option>
        </select>
      </label>

      <h2>Services et options</h2>
      <label><input type="checkbox" id="packing"> Emballage / Déballage</label>
      <label><input type="checkbox" id="assembly"> Montage / Démontage</label>
      <label><input type="checkbox" id="fragile"> Protection spéciale</label>

      <h2>Inventaire</h2>
      <div id="inventoryContainer">
        <div class="inventoryItem">
          <input type="text" placeholder="Description" class="itemDesc">
          <input type="number" placeholder="Quantité" class="itemQty">
          <select class="itemAssembly">
            <option value="Non">Assemblage: Non</option>
            <option value="Oui">Assemblage: Oui</option>
          </select>
        </div>
      </div>
      <button type="button" id="addItem">Ajouter un objet</button>

      <br><br>
      <button type="button" onclick="generatePDF()">Générer PDF</button>
    </form>
  </div>

  <script src="script.js"></script>
</body>
</html>
