/* style.css */

/* General page styling */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 900px;
    margin: 30px auto;
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1, h2 {
    color: #333;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
}

h2 {
    margin-top: 20px;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

/* Form styling */
form label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

form input[type="text"],
form input[type="number"],
form select {
    width: 100%;
    padding: 8px 10px;
    margin-top: 5px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

form input[type="checkbox"] {
    margin-right: 10px;
}

#addItem, button[type="button"] {
    display: inline-block;
    padding: 10px 20px;
    margin-top: 10px;
    margin-bottom: 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

#addItem:hover,
button[type="button"]:hover {
    background-color: #218838;
}

/* Inventory items container */
.inventoryItem {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.inventoryItem input,
.inventoryItem select {
    flex: 1;
}

/* Responsive */
@media (max-width: 600px) {
    .inventoryItem {
        flex-direction: column;
    }
}
